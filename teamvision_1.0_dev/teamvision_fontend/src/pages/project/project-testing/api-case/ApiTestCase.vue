<template>
  <div :style="'height:' + containerHeight + 'px; display: flex;'">
    <Layout>
      <Sider hide-trigger :width="testCasePlanWidth" :style="{ background: '#fff', 'border-left': '1px solid #fff', }">
        <api-case-panel :projectID="projectID"></api-case-panel>
      </Sider>
      <Content>
        <api-case-content :projectID="projectID" :groupID="groupID" :caseID="caseID"></api-case-content>
      </Content>
    </Layout>
  </div>
</template>

<script>
import ApiCasePanel from "./ApiCasePanel.vue";
import ApiCaseContent from "./ApiCaseContent.vue";
import { mapGetters, mapMutations, mapActions, mapState } from "vuex";

export default {
  name: "ApiCase",
  props: [
    "projectID",
  ],
  data() {
    return {
      testCasePlanWidth: 360,
    }
  },
  computed: {
    ...mapState(['appBodyMainHeight', 'appBodyWidth', 'appBodyHeight']),
    ...mapState('testcase', ['testCasePlanShow']),
    containerHeight: function () {
      return this.appBodyHeight
    },
  },
  methods: {
    ...mapActions('', [''])
  },

  created: function () {
    //console.log('ProjectTesting.vue created')
    if (this.projectID == undefined) {
      this.projectID = parseInt(this.$route.params.projectID)
    }
  },

  watch: {
    testCasePlanShow: function (value) {
      if (value == true) {
        this.testCasePlanWidth = 360;
      } else {
        this.testCasePlanWidth = 0;
      }
    },
  },
  components: {
    ApiCasePanel,
    ApiCaseContent
  }
}
</script>
