## API 测试用例（API Test Case）PRD

### 1. 背景与目标
- **背景**：项目需要在 TeamVision 内快速编写、组织、执行与复用 API 测试用例，支撑日常联调、回归与发布前验证。现有需求希望提供一个与 Postman/Hoppscotch 习惯相近、学习成本低的测试工作台，并与项目数据、权限体系打通。
- **总体目标**：
  - 支持环境与变量管理，避免多环境切换时重复改参数。
  - 支持请求/用例的集合管理、参数配置、脚本与断言，形成可复用资产。
  - 支持单次执行与批量执行，记录历史与结果，便于回溯与对比。
  - 支持团队协作、权限与审计，保证数据安全与可控。

### 2. 参考输入（设计图要点）
依据提供的三张设计图，总结关键界面与元素：
- 新建环境弹窗：
  - 字段：`Label`（环境名称），`Variables` 与 `Secrets` 两个页签。
  - 变量示例：`user = zhang`，`password = 123456`。
  - 操作：新增变量、删除变量、复制变量值、校验变量等。
- 请求工作台：
  - 顶部：请求方法 `GET`，请求地址 `https://echo.hoppscotch.io/login`，可选择环境（Select environment），按钮 `Send` 与保存。
  - 选项卡：`Parameters`、`Body`、`Headers`、`Authorization`、`Pre-request Script`、`Post-request Script`。
  - 侧栏：`Variables` 快捷面板（插入变量、校验变量是否可用）。
- 集合/用例：
  - 右侧集合树包含文件夹 `login` 与请求项 `GET login`（已保存）。

以上要点需要在产品中得到实现或等价实现，并与 TeamVision 的页面结构集成：`ApiTestCase.vue` 由 `ApiCasePanel`（左侧用例/集合面板）与 `ApiCaseContent`（右侧请求工作台）组成。

### 3. 用户与场景
- **用户**：
  - 开发、测试、实施工程师；少量产品或运维人员会使用只读或触发执行能力。
- **核心使用场景**：
  1) 在不同环境（如 dev/test/stage/prod）间切换请求目标与凭据。
  2) 在集合中组织接口请求与测试用例，支持层级文件夹与搜索。
  3) 在请求中编写参数、头、鉴权、前置/后置脚本与断言；执行并查看响应与断言结果。
  4) 将请求变量化（如 `{{user}}`、`{{password}}`），通过环境/Secrets 注入。
  5) 批量执行一组用例，出具汇总报告，记录历史。

### 4. 术语
- **环境（Environment）**：一组变量与 Secrets 的集合，按标签区分，如 `dev`、`test`、`prod`。
- **变量（Variable）/Secrets**：键值对；Secrets 为敏感值，仅掩码显示与权限控制。
- **集合（Collection）**：按项目组织请求/测试用例的树状结构。
- **请求（Request）**：一次 HTTP 调用的配置（方法、URL、参数、Body、Headers、Auth 等）。
- **测试用例（Test Case）**：在请求基础上添加断言、前后置脚本、数据驱动等测试逻辑的实体。
- **运行（Run）/报告（Report）**：一次或多次用例执行后的结果聚合产物。

### 5. 功能需求

#### 5.1 环境与变量管理
- 新增/编辑/删除环境：字段包括 `label`、`variables`、`secrets`。
- 变量增删改查：键（必填，唯一）、值、描述、是否敏感（敏感进入 Secrets）。
- 变量引用：在 URL、参数、Body、Headers、脚本中以 `{{varName}}` 调用。
- 变量优先级：用例局部变量 > 环境变量 > 全局变量（若将来提供全局）。
- 变量快速插入：在工作台右侧 `Variables` 面板点击即可拷贝或插入到光标处。
- 环境切换：请求栏可选择环境；切换后即时生效并提示变量解析情况。
- 安全：Secrets 值默认掩码显示；导出时可选择是否包含敏感值。

#### 5.2 集合与用例组织（左侧 `ApiCasePanel`）
- 结构：项目内多集合；集合内可建文件夹与请求/用例条目。
- 操作：新增/重命名/移动/复制/删除；拖拽排序；搜索过滤。
- 引用：同一请求可复制为用例模板，或跨集合复制。
- 状态标记：草稿/已保存；最近更新时间、最近执行状态（成功/失败）。

#### 5.3 请求工作台（右侧 `ApiCaseContent`）
- 顶栏：方法选择、URL 输入、环境选择、`Send`、`Save`、查看响应时间/大小。
- 选项卡：
  - Parameters：键值对，支持 query 拼接、是否启用、描述。
  - Body：form-data/x-www-form-urlencoded/raw（json/text/xml）/binary，支持示例与模板。
  - Headers：键值对，支持常用头快捷添加。
  - Authorization：无/Basic/Bearer/API Key/OAuth2（初期至少无、Basic、Bearer）。
  - Pre-request Script：执行于发送前，JS 运行环境，可读写变量与请求属性。
  - Post-request Script：执行于响应后，JS 环境，支持断言与结果写回变量。
- 响应展示：状态码、时间、大小、Headers、Body（JSON 高亮/折叠/搜索）、`Copy`。

#### 5.4 断言与测试
- 断言类型：
  - 状态码断言（=、范围、集合）。
  - Headers 断言（存在/值等于/正则）。
  - Body 断言（JSONPath/路径提取、包含、等于、长度、数值比较）。
  - 性能断言（响应时间阈值）。
- 断言可由 Post-request Script 以代码形式编写，也可在可视化断言面板配置（优先提供脚本方式）。
- 执行结果：每条断言通过/失败信息，失败展示实际值与期望值。

#### 5.5 数据驱动与变量提取
- 从响应中提取数据写入变量（环境级/运行级/用例级可选），用于后续请求串联。
- 数据源：内置表格/CSV 导入（V1 可选做，仅保留单例执行作为起步）。

#### 5.6 执行与历史
- 单请求执行：在工作台点击 `Send`。
- 用例执行：在集合树右键或详情中触发执行；支持选中多条批量执行（V1 可以先支持单请求/单用例执行）。
- 历史记录：保存请求配置快照、响应摘要、断言结果；可对比两次执行。

#### 5.7 导入/导出
- 导出集合：JSON；可选择是否带 Secrets 值。
- 导入：兼容自身导出格式；后续考虑兼容 Postman/Hoppscotch（V2）。

#### 5.8 权限与审计
- 读写权限：继承项目权限；仅维护者可管理环境与 Secrets。
- 审计：环境变更、集合/用例增删改、执行操作留痕（操作者、时间、摘要）。

### 6. 与现有前端结构的关系
- 页面容器：`src/pages/project/project-testing/api-testcase/ApiTestCase.vue`。
  - 左侧 `ApiCasePanel`：集合/用例树、搜索、右键菜单。
  - 右侧 `ApiCaseContent`：请求编辑与执行工作台、变量侧栏、响应区与断言区。
- 布局自适应：跟随 `appBodyHeight`，当 `testCasePlanShow` 为 false 时左侧宽度为 0。

### 7. 交互与文案细节
- 新建环境：
  - 弹窗包含 `Label`、`Variables`、`Secrets` 页签；每条变量含 `Key`、`Value`、`Description`、`Enable`。
  - 操作包含：新增、删除、从剪贴板粘贴 KV、多选删除、校验重复 Key。
  - 提交校验：必填项、Key 唯一、长度限制，失败时定位到行并提示。
- 请求栏：
  - URL 支持变量占位符；输入时实时高亮未解析变量；悬浮提示解析后 URL。
  - 切换环境时提示“X 个变量解析成功 / Y 个未找到”。
- 发送请求：
  - 发送中按钮进入加载态；超时显示重试入口；支持取消。
  - 保存请求时若包含未保存变量变更，提示“是否同步保存环境”。
- 断言结果：
  - 以列表展示，每条展示状态、表达式、期望/实际、耗时；支持复制。

### 8. 数据模型（逻辑）
（用于对后端接口或本地存储建模，命名仅供参考）
- Environment
  - id, projectId, label, variables[ { key, value, desc, isSecret } ], updatedBy, updatedAt
- Collection
  - id, projectId, name, parentId, sort
- Request
  - id, collectionId, name, method, url, params[], headers[], body, auth, preScript, postScript
- TestCase
  - id, requestId, name, assertions[], enabled, lastRunStatus, lastRunAt
- RunResult
  - id, testCaseId, status, durationMs, responseMeta, assertionResults[]

### 9. 非功能需求
- **性能**：单请求首包 < 1.5s（内网 API），批量执行 50 条以内 UI 不卡顿。
- **可靠性**：请求异常可重试；编辑态自动保存草稿（本地缓存）。
- **安全**：Secrets 加密存储与传输；仅授权用户可见与导出；掩码显示。
- **可用性**：支持快捷键（发送、保存、搜索）；暗色主题适配。
- **兼容性**：Chrome 最新两个大版本；分辨率 ≥ 1366×768 良好展示。

### 10. 验收标准（以登录示例验证）
1) 能创建名为 `login` 的环境，新增变量 `user=zhang`、`password=123456`；变量行校验与提示正常。
2) 在请求栏选择该环境，输入 `GET https://echo.hoppscotch.io/login`，在 Parameters/Headers 中可插入 `{{user}}`、`{{password}}` 并正确解析。
3) 点击 `Send` 可得到响应；响应区高亮 JSON，显示状态码、时间与大小。
4) 在 Post-request Script 中编写一个状态码为 200 的断言并通过；失败时可看到期望/实际对比。
5) 将请求保存为集合 `login/GET login`；在左侧面板能看到并能再次打开执行，历史可回看。

### 11. 里程碑（V1）
- M1：环境与变量管理、请求工作台（Parameters/Body/Headers/Auth）、发送与响应视图。
- M2：脚本与断言（至少脚本式）、集合组织与保存、历史记录。
- M3：权限与审计、导入/导出（自有格式）。

### 12. 边界与不做（V1）
- 不做：完整的 OAuth2 授权流程引导（可仅支持 Token 粘贴）；高级数据驱动（CSV 批量）仅规划，V1 可不做；与外部测试平台的联动暂不做。

### 13. 指标与观察
- 使用率：月活使用人数、创建的集合/用例数量、执行次数。
- 质量：断言通过率、失败原因分类（网络/断言/脚本错误）。
- 体验：平均响应查看时间、脚本错误率、变量解析失败次数。

### 14. 未决问题
- 是否需要与项目内“接口管理”模块互通（导入接口定义生成请求）？
- 断言是否需要提供可视化面板（与脚本并存）的优先级与时间点？
- 历史数据的保留周期与存储配额策略？


