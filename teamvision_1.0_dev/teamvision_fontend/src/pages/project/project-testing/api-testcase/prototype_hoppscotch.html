<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TeamVision API 测试用例 - Hoppscotch Style</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #0f172a;
            color: #e2e8f0;
            height: 100vh;
            overflow: hidden;
        }

        .app-container {
            display: flex;
            height: 100vh;
        }

        /* 左侧面板 */
        .sidebar {
            width: 300px;
            background: #1e293b;
            border-right: 1px solid #334155;
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 16px;
            border-bottom: 1px solid #334155;
        }

        .sidebar-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 12px;
        }

        .environment-selector {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .env-select {
            background: #334155;
            border: 1px solid #475569;
            border-radius: 6px;
            color: #e2e8f0;
            padding: 8px 12px;
            flex: 1;
            font-size: 14px;
        }

        .btn-new-env {
            background: #3b82f6;
            border: none;
            border-radius: 6px;
            color: white;
            padding: 8px 12px;
            cursor: pointer;
            font-size: 12px;
        }

        .btn-new-env:hover {
            background: #2563eb;
        }

        .collections-section {
            flex: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .collections-header {
            padding: 16px;
            border-bottom: 1px solid #334155;
            display: flex;
            justify-content: between;
            align-items: center;
        }

        .collections-title {
            font-size: 14px;
            font-weight: 500;
            color: #94a3b8;
        }

        .btn-new-collection {
            background: transparent;
            border: 1px solid #475569;
            border-radius: 4px;
            color: #94a3b8;
            padding: 4px 8px;
            cursor: pointer;
            font-size: 12px;
        }

        .collections-tree {
            flex: 1;
            overflow-y: auto;
            padding: 8px;
        }

        .collection-item {
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            margin: 2px 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .collection-item:hover {
            background: #334155;
        }

        .collection-item.active {
            background: #3b82f6;
        }

        .collection-icon {
            width: 16px;
            height: 16px;
            opacity: 0.6;
        }

        .method-tag {
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: 500;
        }

        .method-get {
            background: #059669;
            color: white;
        }

        .method-post {
            background: #dc2626;
            color: white;
        }

        .method-put {
            background: #d97706;
            color: white;
        }

        .method-delete {
            background: #dc2626;
            color: white;
        }

        /* 主内容区 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .request-bar {
            padding: 16px;
            background: #1e293b;
            border-bottom: 1px solid #334155;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .method-selector {
            background: #334155;
            border: 1px solid #475569;
            border-radius: 6px;
            color: #e2e8f0;
            padding: 8px 12px;
            font-size: 14px;
            font-weight: 500;
        }

        .url-input {
            flex: 1;
            background: #334155;
            border: 1px solid #475569;
            border-radius: 6px;
            color: #e2e8f0;
            padding: 8px 12px;
            font-size: 14px;
        }

        .url-input:focus {
            outline: none;
            border-color: #3b82f6;
        }

        .btn-send {
            background: #10b981;
            border: none;
            border-radius: 6px;
            color: white;
            padding: 8px 24px;
            cursor: pointer;
            font-weight: 500;
            font-size: 14px;
        }

        .btn-send:hover {
            background: #059669;
        }

        .btn-save {
            background: transparent;
            border: 1px solid #475569;
            border-radius: 6px;
            color: #e2e8f0;
            padding: 8px 16px;
            cursor: pointer;
            font-size: 14px;
        }

        .request-tabs {
            display: flex;
            background: #1e293b;
            border-bottom: 1px solid #334155;
        }

        .request-tab {
            padding: 12px 16px;
            background: transparent;
            border: none;
            color: #94a3b8;
            cursor: pointer;
            font-size: 14px;
            border-bottom: 2px solid transparent;
        }

        .request-tab.active {
            color: #3b82f6;
            border-bottom-color: #3b82f6;
        }

        .request-tab:hover {
            color: #e2e8f0;
        }

        .request-content {
            flex: 1;
            display: flex;
            overflow: hidden;
        }

        .request-body {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .tab-content {
            flex: 1;
            padding: 16px;
            overflow-y: auto;
        }

        .param-row {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
        }

        .param-checkbox {
            width: 16px;
            height: 16px;
        }

        .param-input {
            flex: 1;
            background: #334155;
            border: 1px solid #475569;
            border-radius: 4px;
            color: #e2e8f0;
            padding: 8px 12px;
            font-size: 14px;
        }

        .param-input::placeholder {
            color: #64748b;
        }

        .btn-add-param {
            background: transparent;
            border: 1px solid #475569;
            border-radius: 4px;
            color: #94a3b8;
            padding: 8px 16px;
            cursor: pointer;
            font-size: 14px;
            margin-top: 8px;
        }

        .code-editor {
            background: #0f172a;
            border: 1px solid #334155;
            border-radius: 6px;
            color: #e2e8f0;
            padding: 16px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            min-height: 200px;
            resize: vertical;
        }

        /* 变量侧边栏 */
        .variables-sidebar {
            width: 250px;
            background: #1e293b;
            border-left: 1px solid #334155;
            display: flex;
            flex-direction: column;
        }

        .variables-header {
            padding: 16px;
            border-bottom: 1px solid #334155;
        }

        .variables-title {
            font-size: 14px;
            font-weight: 500;
            color: #94a3b8;
            margin-bottom: 8px;
        }

        .variable-search {
            background: #334155;
            border: 1px solid #475569;
            border-radius: 4px;
            color: #e2e8f0;
            padding: 6px 8px;
            font-size: 12px;
            width: 100%;
        }

        .variables-list {
            flex: 1;
            padding: 8px;
            overflow-y: auto;
        }

        .variable-item {
            padding: 8px;
            border-radius: 4px;
            margin: 2px 0;
            cursor: pointer;
            border: 1px solid transparent;
        }

        .variable-item:hover {
            background: #334155;
            border-color: #475569;
        }

        .variable-key {
            font-size: 12px;
            font-weight: 500;
            color: #3b82f6;
        }

        .variable-value {
            font-size: 11px;
            color: #94a3b8;
            margin-top: 2px;
        }

        /* 响应区域 */
        .response-section {
            height: 50%;
            border-top: 1px solid #334155;
            display: flex;
            flex-direction: column;
        }

        .response-header {
            padding: 12px 16px;
            background: #1e293b;
            border-bottom: 1px solid #334155;
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .response-status {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-code {
            background: #059669;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .response-meta {
            font-size: 12px;
            color: #94a3b8;
        }

        .response-tabs {
            display: flex;
            background: #1e293b;
        }

        .response-tab {
            padding: 8px 16px;
            background: transparent;
            border: none;
            color: #94a3b8;
            cursor: pointer;
            font-size: 12px;
            border-bottom: 2px solid transparent;
        }

        .response-tab.active {
            color: #3b82f6;
            border-bottom-color: #3b82f6;
        }

        .response-body {
            flex: 1;
            padding: 16px;
            overflow-y: auto;
            background: #0f172a;
        }

        .json-viewer {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            line-height: 1.5;
        }

        /* 弹窗样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .modal-overlay.show {
            display: flex;
        }

        .modal {
            background: #1e293b;
            border-radius: 8px;
            width: 600px;
            max-width: 90vw;
            max-height: 80vh;
            overflow: hidden;
            border: 1px solid #334155;
        }

        .modal-header {
            padding: 16px;
            border-bottom: 1px solid #334155;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 16px;
            font-weight: 600;
        }

        .btn-close {
            background: transparent;
            border: none;
            color: #94a3b8;
            cursor: pointer;
            font-size: 18px;
        }

        .modal-body {
            padding: 16px;
            max-height: 60vh;
            overflow-y: auto;
        }

        .modal-tabs {
            display: flex;
            margin-bottom: 16px;
            border-bottom: 1px solid #334155;
        }

        .modal-tab {
            padding: 8px 16px;
            background: transparent;
            border: none;
            color: #94a3b8;
            cursor: pointer;
            font-size: 14px;
            border-bottom: 2px solid transparent;
        }

        .modal-tab.active {
            color: #3b82f6;
            border-bottom-color: #3b82f6;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-label {
            display: block;
            margin-bottom: 4px;
            font-size: 14px;
            font-weight: 500;
        }

        .form-input {
            width: 100%;
            background: #334155;
            border: 1px solid #475569;
            border-radius: 4px;
            color: #e2e8f0;
            padding: 8px 12px;
            font-size: 14px;
        }

        .modal-footer {
            padding: 16px;
            border-top: 1px solid #334155;
            display: flex;
            justify-content: flex-end;
            gap: 8px;
        }

        .btn-primary {
            background: #3b82f6;
            border: none;
            border-radius: 4px;
            color: white;
            padding: 8px 16px;
            cursor: pointer;
            font-size: 14px;
        }

        .btn-secondary {
            background: transparent;
            border: 1px solid #475569;
            border-radius: 4px;
            color: #e2e8f0;
            padding: 8px 16px;
            cursor: pointer;
            font-size: 14px;
        }

        .hidden {
            display: none !important;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                position: absolute;
                z-index: 100;
                height: 100%;
                transform: translateX(-100%);
                transition: transform 0.3s;
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .variables-sidebar {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- 左侧面板 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2 class="sidebar-title">API 测试用例</h2>
                <div class="environment-selector">
                    <select class="env-select" id="envSelect">
                        <option value="">选择环境</option>
                        <option value="dev">开发环境</option>
                        <option value="test">测试环境</option>
                        <option value="prod">生产环境</option>
                    </select>
                    <button class="btn-new-env" onclick="showNewEnvModal()">新建</button>
                </div>
            </div>
            
            <div class="collections-section">
                <div class="collections-header">
                    <h3 class="collections-title">集合</h3>
                    <button class="btn-new-collection" onclick="showNewCollectionModal()">新建</button>
                </div>
                <div class="collections-tree">
                    <div class="collection-item">
                        <span class="collection-icon">📁</span>
                        <span>用户管理</span>
                    </div>
                    <div class="collection-item active">
                        <span class="collection-icon">📁</span>
                        <span>登录模块</span>
                    </div>
                    <div class="collection-item" style="margin-left: 20px;">
                        <span class="method-tag method-get">GET</span>
                        <span>用户登录</span>
                    </div>
                    <div class="collection-item" style="margin-left: 20px;">
                        <span class="method-tag method-post">POST</span>
                        <span>用户注册</span>
                    </div>
                    <div class="collection-item">
                        <span class="collection-icon">📁</span>
                        <span>订单管理</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 请求栏 -->
            <div class="request-bar">
                <select class="method-selector">
                    <option>GET</option>
                    <option>POST</option>
                    <option>PUT</option>
                    <option>DELETE</option>
                    <option>PATCH</option>
                </select>
                <input type="text" class="url-input" placeholder="输入请求 URL" value="https://echo.hoppscotch.io/login">
                <button class="btn-send" onclick="sendRequest()">发送</button>
                <button class="btn-save">保存</button>
            </div>

            <!-- 请求选项卡 -->
            <div class="request-tabs">
                <button class="request-tab active" onclick="switchTab('params')">Parameters</button>
                <button class="request-tab" onclick="switchTab('body')">Body</button>
                <button class="request-tab" onclick="switchTab('headers')">Headers</button>
                <button class="request-tab" onclick="switchTab('auth')">Authorization</button>
                <button class="request-tab" onclick="switchTab('pre-script')">Pre-request Script</button>
                <button class="request-tab" onclick="switchTab('post-script')">Post-request Script</button>
            </div>

            <div class="request-content">
                <div class="request-body">
                    <!-- Parameters 选项卡 -->
                    <div class="tab-content" id="params-tab">
                        <div class="param-row">
                            <input type="checkbox" class="param-checkbox" checked>
                            <input type="text" class="param-input" placeholder="参数名" value="username">
                            <input type="text" class="param-input" placeholder="参数值" value="{{user}}">
                            <input type="text" class="param-input" placeholder="描述">
                        </div>
                        <div class="param-row">
                            <input type="checkbox" class="param-checkbox" checked>
                            <input type="text" class="param-input" placeholder="参数名" value="password">
                            <input type="text" class="param-input" placeholder="参数值" value="{{password}}">
                            <input type="text" class="param-input" placeholder="描述">
                        </div>
                        <div class="param-row">
                            <input type="checkbox" class="param-checkbox">
                            <input type="text" class="param-input" placeholder="参数名">
                            <input type="text" class="param-input" placeholder="参数值">
                            <input type="text" class="param-input" placeholder="描述">
                        </div>
                        <button class="btn-add-param">+ 添加参数</button>
                    </div>

                    <!-- Body 选项卡 -->
                    <div class="tab-content hidden" id="body-tab">
                        <div style="margin-bottom: 12px;">
                            <select class="method-selector">
                                <option>none</option>
                                <option>form-data</option>
                                <option>x-www-form-urlencoded</option>
                                <option>raw</option>
                                <option>binary</option>
                            </select>
                        </div>
                        <textarea class="code-editor" placeholder="请求体内容">{
  "username": "{{user}}",
  "password": "{{password}}"
}</textarea>
                    </div>

                    <!-- Headers 选项卡 -->
                    <div class="tab-content hidden" id="headers-tab">
                        <div class="param-row">
                            <input type="checkbox" class="param-checkbox" checked>
                            <input type="text" class="param-input" placeholder="Header 名" value="Content-Type">
                            <input type="text" class="param-input" placeholder="Header 值" value="application/json">
                        </div>
                        <div class="param-row">
                            <input type="checkbox" class="param-checkbox">
                            <input type="text" class="param-input" placeholder="Header 名">
                            <input type="text" class="param-input" placeholder="Header 值">
                        </div>
                        <button class="btn-add-param">+ 添加 Header</button>
                    </div>

                    <!-- Authorization 选项卡 -->
                    <div class="tab-content hidden" id="auth-tab">
                        <div class="form-group">
                            <label class="form-label">认证类型</label>
                            <select class="form-input">
                                <option>None</option>
                                <option>Basic Auth</option>
                                <option>Bearer Token</option>
                                <option>API Key</option>
                            </select>
                        </div>
                    </div>

                    <!-- Pre-request Script 选项卡 -->
                    <div class="tab-content hidden" id="pre-script-tab">
                        <textarea class="code-editor" placeholder="// 请求前执行的脚本">// 设置动态变量
pm.environment.set('timestamp', Date.now().toString());

// 生成随机数
pm.environment.set('random', Math.random().toString());</textarea>
                    </div>

                    <!-- Post-request Script 选项卡 -->
                    <div class="tab-content hidden" id="post-script-tab">
                        <textarea class="code-editor" placeholder="// 请求后执行的脚本和断言">// 状态码断言
pm.test("状态码应为 200", function () {
    pm.response.to.have.status(200);
});

// 响应时间断言
pm.test("响应时间应小于 2000ms", function () {
    pm.expect(pm.response.responseTime).to.be.below(2000);
});

// JSON 响应断言
pm.test("响应应包含 success 字段", function () {
    var jsonData = pm.response.json();
    pm.expect(jsonData).to.have.property('success');
});</textarea>
                    </div>
                </div>

                <!-- 变量侧边栏 -->
                <div class="variables-sidebar">
                    <div class="variables-header">
                        <h3 class="variables-title">环境变量</h3>
                        <input type="text" class="variable-search" placeholder="搜索变量...">
                    </div>
                    <div class="variables-list">
                        <div class="variable-item" onclick="insertVariable('user')">
                            <div class="variable-key">user</div>
                            <div class="variable-value">zhang</div>
                        </div>
                        <div class="variable-item" onclick="insertVariable('password')">
                            <div class="variable-key">password</div>
                            <div class="variable-value">******</div>
                        </div>
                        <div class="variable-item" onclick="insertVariable('baseUrl')">
                            <div class="variable-key">baseUrl</div>
                            <div class="variable-value">https://api.example.com</div>
                        </div>
                        <div class="variable-item" onclick="insertVariable('token')">
                            <div class="variable-key">token</div>
                            <div class="variable-value">******</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 响应区域 -->
            <div class="response-section">
                <div class="response-header">
                    <div class="response-status">
                        <span class="status-code">200 OK</span>
                        <span class="response-meta">时间: 245ms</span>
                        <span class="response-meta">大小: 1.2KB</span>
                    </div>
                </div>
                <div class="response-tabs">
                    <button class="response-tab active">响应体</button>
                    <button class="response-tab">响应头</button>
                    <button class="response-tab">断言结果</button>
                    <button class="response-tab">执行日志</button>
                </div>
                <div class="response-body">
                    <div class="json-viewer">
{
  "success": true,
  "message": "登录成功",
  "data": {
    "userId": "12345",
    "username": "zhang",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires": "2024-01-01T00:00:00Z"
  }
}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新建环境弹窗 -->
    <div class="modal-overlay" id="newEnvModal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">新建环境</h3>
                <button class="btn-close" onclick="hideNewEnvModal()">×</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">环境名称</label>
                    <input type="text" class="form-input" placeholder="例如：开发环境">
                </div>
                
                <div class="modal-tabs">
                    <button class="modal-tab active" onclick="switchEnvTab('variables')">Variables</button>
                    <button class="modal-tab" onclick="switchEnvTab('secrets')">Secrets</button>
                </div>

                <div id="variables-content">
                    <div class="param-row">
                        <input type="checkbox" class="param-checkbox" checked>
                        <input type="text" class="param-input" placeholder="变量名" value="user">
                        <input type="text" class="param-input" placeholder="变量值" value="zhang">
                        <input type="text" class="param-input" placeholder="描述">
                    </div>
                    <div class="param-row">
                        <input type="checkbox" class="param-checkbox" checked>
                        <input type="text" class="param-input" placeholder="变量名" value="password">
                        <input type="text" class="param-input" placeholder="变量值" value="123456">
                        <input type="text" class="param-input" placeholder="描述">
                    </div>
                    <div class="param-row">
                        <input type="checkbox" class="param-checkbox">
                        <input type="text" class="param-input" placeholder="变量名">
                        <input type="text" class="param-input" placeholder="变量值">
                        <input type="text" class="param-input" placeholder="描述">
                    </div>
                    <button class="btn-add-param">+ 添加变量</button>
                </div>

                <div id="secrets-content" class="hidden">
                    <div class="param-row">
                        <input type="checkbox" class="param-checkbox" checked>
                        <input type="text" class="param-input" placeholder="密钥名" value="apiKey">
                        <input type="password" class="param-input" placeholder="密钥值" value="sk-1234567890">
                        <input type="text" class="param-input" placeholder="描述">
                    </div>
                    <div class="param-row">
                        <input type="checkbox" class="param-checkbox">
                        <input type="text" class="param-input" placeholder="密钥名">
                        <input type="password" class="param-input" placeholder="密钥值">
                        <input type="text" class="param-input" placeholder="描述">
                    </div>
                    <button class="btn-add-param">+ 添加密钥</button>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" onclick="hideNewEnvModal()">取消</button>
                <button class="btn-primary">保存环境</button>
            </div>
        </div>
    </div>

    <!-- 新建集合弹窗 -->
    <div class="modal-overlay" id="newCollectionModal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">新建集合</h3>
                <button class="btn-close" onclick="hideNewCollectionModal()">×</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">集合名称</label>
                    <input type="text" class="form-input" placeholder="例如：用户管理API">
                </div>
                <div class="form-group">
                    <label class="form-label">描述</label>
                    <textarea class="form-input" rows="3" placeholder="集合描述（可选）"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" onclick="hideNewCollectionModal()">取消</button>
                <button class="btn-primary">创建集合</button>
            </div>
        </div>
    </div>

    <script>
        // 切换请求选项卡
        function switchTab(tabName) {
            // 隐藏所有选项卡内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.add('hidden');
            });
            
            // 移除所有选项卡的激活状态
            document.querySelectorAll('.request-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示目标选项卡内容
            document.getElementById(tabName + '-tab').classList.remove('hidden');
            
            // 激活目标选项卡
            event.target.classList.add('active');
        }

        // 切换环境弹窗选项卡
        function switchEnvTab(tabName) {
            document.querySelectorAll('.modal-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            event.target.classList.add('active');
            
            if (tabName === 'variables') {
                document.getElementById('variables-content').classList.remove('hidden');
                document.getElementById('secrets-content').classList.add('hidden');
            } else {
                document.getElementById('variables-content').classList.add('hidden');
                document.getElementById('secrets-content').classList.remove('hidden');
            }
        }

        // 显示新建环境弹窗
        function showNewEnvModal() {
            document.getElementById('newEnvModal').classList.add('show');
        }

        // 隐藏新建环境弹窗
        function hideNewEnvModal() {
            document.getElementById('newEnvModal').classList.remove('show');
        }

        // 显示新建集合弹窗
        function showNewCollectionModal() {
            document.getElementById('newCollectionModal').classList.add('show');
        }

        // 隐藏新建集合弹窗
        function hideNewCollectionModal() {
            document.getElementById('newCollectionModal').classList.remove('show');
        }

        // 插入变量
        function insertVariable(varName) {
            const activeInput = document.activeElement;
            if (activeInput && activeInput.tagName === 'INPUT' || activeInput.tagName === 'TEXTAREA') {
                const cursorPos = activeInput.selectionStart;
                const textBefore = activeInput.value.substring(0, cursorPos);
                const textAfter = activeInput.value.substring(cursorPos);
                activeInput.value = textBefore + '{{' + varName + '}}' + textAfter;
                activeInput.focus();
                activeInput.setSelectionRange(cursorPos + varName.length + 4, cursorPos + varName.length + 4);
            } else {
                // 如果没有焦点输入框，复制到剪贴板
                navigator.clipboard.writeText('{{' + varName + '}}').then(() => {
                    alert('变量已复制到剪贴板：{{' + varName + '}}');
                });
            }
        }

        // 发送请求
        function sendRequest() {
            const btn = event.target;
            btn.textContent = '发送中...';
            btn.disabled = true;
            
            // 模拟请求延迟
            setTimeout(() => {
                btn.textContent = '发送';
                btn.disabled = false;
                alert('请求已发送！查看响应区域的结果。');
            }, 1500);
        }

        // 点击弹窗外部关闭
        document.addEventListener('click', function(event) {
            if (event.target.classList.contains('modal-overlay')) {
                event.target.classList.remove('show');
            }
        });

        // 键盘快捷键
        document.addEventListener('keydown', function(event) {
            if (event.ctrlKey || event.metaKey) {
                switch(event.key) {
                    case 'Enter':
                        event.preventDefault();
                        sendRequest();
                        break;
                    case 's':
                        event.preventDefault();
                        alert('保存快捷键触发');
                        break;
                }
            }
        });
    </script>
</body>
</html>
